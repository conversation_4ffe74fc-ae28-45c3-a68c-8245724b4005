import 'package:flutter/material.dart';
import 'package:farautorentify/models/vehicle_model.dart';
import 'package:farautorentify/services/database_service.dart';
import 'package:farautorentify/screens/admin/add_vehicle_screen.dart';
import 'package:farautorentify/screens/admin/edit_vehicle_screen.dart';
import 'package:farautorentify/utils/constants.dart';
import 'package:farautorentify/widgets/custom_button.dart';

class VehiclesManagementScreen extends StatefulWidget {
  const VehiclesManagementScreen({super.key});

  @override
  State<VehiclesManagementScreen> createState() =>
      _VehiclesManagementScreenState();
}

class _VehiclesManagementScreenState extends State<VehiclesManagementScreen> {
  final DatabaseService _databaseService = DatabaseService();
  String _searchQuery = '';
  String _selectedTypeFilter = 'All';
  String _selectedStatusFilter = 'All';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      body: Column(
        children: [
          // Search and Filter Bar
          _buildSearchAndFilterBar(),

          // Vehicles List
          Expanded(child: _buildVehiclesList()),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _navigateToAddVehicle(),
        backgroundColor: AppConstants.primaryColor,
        foregroundColor: Colors.white,
        icon: const Icon(Icons.add),
        label: const Text('Add Vehicle'),
      ),
    );
  }

  Widget _buildSearchAndFilterBar() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(bottom: BorderSide(color: Colors.grey, width: 0.2)),
      ),
      child: Column(
        children: [
          // Search Bar
          TextField(
            onChanged: (value) {
              setState(() {
                _searchQuery = value.toLowerCase();
              });
            },
            decoration: InputDecoration(
              hintText: 'Search vehicles by name, brand, or model...',
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(
                  AppConstants.borderRadiusMedium,
                ),
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: AppConstants.paddingMedium,
                vertical: AppConstants.paddingSmall,
              ),
            ),
          ),

          const SizedBox(height: AppConstants.paddingSmall),

          // Filter Chips
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                const Text(
                  'Type: ',
                  style: TextStyle(
                    fontSize: AppConstants.fontSizeMedium,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                ...['All', 'Cars', 'Bikes'].map((filter) {
                  return Padding(
                    padding: const EdgeInsets.only(
                      right: AppConstants.paddingSmall,
                    ),
                    child: FilterChip(
                      label: Text(filter),
                      selected: _selectedTypeFilter == filter,
                      onSelected: (selected) {
                        setState(() {
                          _selectedTypeFilter = filter;
                        });
                      },
                      selectedColor: AppConstants.primaryColor,
                      checkmarkColor: AppConstants.primaryColor,
                    ),
                  );
                }),

                const SizedBox(width: AppConstants.paddingMedium),

                const Text(
                  'Status: ',
                  style: TextStyle(
                    fontSize: AppConstants.fontSizeMedium,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                ...['All', 'Available', 'Booked'].map((filter) {
                  return Padding(
                    padding: const EdgeInsets.only(
                      right: AppConstants.paddingSmall,
                    ),
                    child: FilterChip(
                      label: Text(filter),
                      selected: _selectedStatusFilter == filter,
                      onSelected: (selected) {
                        setState(() {
                          _selectedStatusFilter = filter;
                        });
                      },
                      selectedColor: AppConstants.secondaryColor,
                      checkmarkColor: AppConstants.secondaryColor,
                    ),
                  );
                }),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVehiclesList() {
    return StreamBuilder<List<VehicleModel>>(
      stream: _databaseService.getAllVehicles(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(
            child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(
                AppConstants.primaryColor,
              ),
            ),
          );
        }

        if (snapshot.hasError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64,
                  color: AppConstants.errorColor,
                ),
                const SizedBox(height: AppConstants.paddingMedium),
                Text(
                  'Error loading vehicles',
                  style: TextStyle(
                    fontSize: AppConstants.fontSizeLarge,
                    color: AppConstants.errorColor,
                  ),
                ),
                const SizedBox(height: AppConstants.paddingSmall),
                Text(
                  snapshot.error.toString(),
                  style: TextStyle(
                    fontSize: AppConstants.fontSizeMedium,
                    color: AppConstants.textSecondaryColor,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

        final vehicles = snapshot.data ?? [];
        final filteredVehicles = _filterVehicles(vehicles);

        if (filteredVehicles.isEmpty) {
          return _buildEmptyState();
        }

        return ListView.builder(
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          itemCount: filteredVehicles.length,
          itemBuilder: (context, index) {
            return _buildVehicleCard(filteredVehicles[index]);
          },
        );
      },
    );
  }

  List<VehicleModel> _filterVehicles(List<VehicleModel> vehicles) {
    return vehicles.where((vehicle) {
      // Apply search filter
      final matchesSearch =
          _searchQuery.isEmpty ||
          vehicle.name.toLowerCase().contains(_searchQuery) ||
          vehicle.brand.toLowerCase().contains(_searchQuery) ||
          vehicle.model.toLowerCase().contains(_searchQuery);

      // Apply type filter
      final matchesType =
          _selectedTypeFilter == 'All' ||
          (_selectedTypeFilter == 'Cars' &&
              vehicle.type == AppConstants.carType) ||
          (_selectedTypeFilter == 'Bikes' &&
              vehicle.type == AppConstants.bikeType);

      // Apply status filter
      final matchesStatus =
          _selectedStatusFilter == 'All' ||
          (_selectedStatusFilter == 'Available' && vehicle.isAvailable) ||
          (_selectedStatusFilter == 'Booked' && !vehicle.isAvailable);

      return matchesSearch && matchesType && matchesStatus;
    }).toList();
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.directions_car_outlined,
            size: 80,
            color: AppConstants.textSecondaryColor,
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          Text(
            _searchQuery.isNotEmpty ||
                    _selectedTypeFilter != 'All' ||
                    _selectedStatusFilter != 'All'
                ? 'No vehicles found matching your criteria'
                : 'No vehicles found',
            style: const TextStyle(
              fontSize: AppConstants.fontSizeXLarge,
              color: AppConstants.textSecondaryColor,
            ),
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Text(
            _searchQuery.isNotEmpty ||
                    _selectedTypeFilter != 'All' ||
                    _selectedStatusFilter != 'All'
                ? 'Try adjusting your search or filters'
                : 'Add your first vehicle to get started',
            style: TextStyle(
              fontSize: AppConstants.fontSizeMedium,
              color: AppConstants.textSecondaryColor,
            ),
            textAlign: TextAlign.center,
          ),
          if (_searchQuery.isEmpty &&
              _selectedTypeFilter == 'All' &&
              _selectedStatusFilter == 'All') ...[
            const SizedBox(height: AppConstants.paddingLarge),
            PrimaryButton(
              text: 'Add Vehicle',
              icon: Icons.add,
              onPressed: () => _navigateToAddVehicle(),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildVehicleCard(VehicleModel vehicle) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
        boxShadow: [
          BoxShadow(
            color: Colors.black,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Vehicle Image
          Container(
            height: 150,
            width: double.infinity,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(AppConstants.borderRadiusLarge),
                topRight: Radius.circular(AppConstants.borderRadiusLarge),
              ),
            ),
            child: Stack(
              children: [
                const Center(
                  child: Icon(
                    Icons.directions_car,
                    size: 60,
                    color: Colors.grey,
                  ),
                ),
                Positioned(
                  top: AppConstants.paddingSmall,
                  right: AppConstants.paddingSmall,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppConstants.paddingSmall,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color:
                          vehicle.isAvailable
                              ? AppConstants.successColor
                              : AppConstants.errorColor,
                      borderRadius: BorderRadius.circular(
                        AppConstants.borderRadiusSmall,
                      ),
                    ),
                    child: Text(
                      vehicle.isAvailable ? 'Available' : 'Booked',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: AppConstants.fontSizeSmall,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
                Positioned(
                  top: AppConstants.paddingSmall,
                  left: AppConstants.paddingSmall,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppConstants.paddingSmall,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color:
                          vehicle.type == AppConstants.carType
                              ? AppConstants.primaryColor
                              : AppConstants.secondaryColor,
                      borderRadius: BorderRadius.circular(
                        AppConstants.borderRadiusSmall,
                      ),
                    ),
                    child: Text(
                      vehicle.type.toUpperCase(),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: AppConstants.fontSizeSmall,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Vehicle Details
          Padding(
            padding: const EdgeInsets.all(AppConstants.paddingMedium),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            vehicle.name,
                            style: const TextStyle(
                              fontSize: AppConstants.fontSizeLarge,
                              fontWeight: FontWeight.bold,
                              color: AppConstants.textPrimaryColor,
                            ),
                          ),
                          Text(
                            '${vehicle.brand} ${vehicle.model} (${vehicle.year})',
                            style: TextStyle(
                              fontSize: AppConstants.fontSizeMedium,
                              color: AppConstants.textSecondaryColor,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          '\$${vehicle.pricePerDay.toStringAsFixed(0)}',
                          style: const TextStyle(
                            fontSize: AppConstants.fontSizeXLarge,
                            fontWeight: FontWeight.bold,
                            color: AppConstants.primaryColor,
                          ),
                        ),
                        Text(
                          'per day',
                          style: TextStyle(
                            fontSize: AppConstants.fontSizeSmall,
                            color: AppConstants.textSecondaryColor,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),

                const SizedBox(height: AppConstants.paddingSmall),

                // Features (first 3)
                if (vehicle.features.isNotEmpty) ...[
                  Wrap(
                    spacing: AppConstants.paddingSmall,
                    runSpacing: 4,
                    children:
                        vehicle.features.take(3).map((feature) {
                          return Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: AppConstants.paddingSmall,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: AppConstants.primaryColor,
                              borderRadius: BorderRadius.circular(
                                AppConstants.borderRadiusSmall,
                              ),
                            ),
                            child: Text(
                              feature,
                              style: TextStyle(
                                fontSize: AppConstants.fontSizeSmall,
                                color: AppConstants.primaryColor,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          );
                        }).toList(),
                  ),
                  const SizedBox(height: AppConstants.paddingMedium),
                ],

                // Action Buttons
                Row(
                  children: [
                    Expanded(
                      child: SecondaryButton(
                        text: 'Edit',
                        icon: Icons.edit,
                        onPressed: () => _navigateToEditVehicle(vehicle),
                      ),
                    ),
                    const SizedBox(width: AppConstants.paddingSmall),
                    Expanded(
                      child: DangerButton(
                        text: 'Delete',
                        icon: Icons.delete,
                        onPressed: () => _confirmDeleteVehicle(vehicle),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _navigateToAddVehicle() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const AddVehicleScreen()),
    );
  }

  void _navigateToEditVehicle(VehicleModel vehicle) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => EditVehicleScreen(vehicle: vehicle),
      ),
    );
  }

  void _confirmDeleteVehicle(VehicleModel vehicle) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Delete Vehicle'),
            content: Text(
              'Are you sure you want to delete "${vehicle.name}"? This action cannot be undone.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () async {
                  Navigator.pop(context);
                  await _deleteVehicle(vehicle);
                },
                style: TextButton.styleFrom(
                  foregroundColor: AppConstants.errorColor,
                ),
                child: const Text('Delete'),
              ),
            ],
          ),
    );
  }

  Future<void> _deleteVehicle(VehicleModel vehicle) async {
    try {
      await _databaseService.deleteVehicle(vehicle.id);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${vehicle.name} has been deleted'),
            backgroundColor: AppConstants.successColor,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to delete vehicle: $e'),
            backgroundColor: AppConstants.errorColor,
          ),
        );
      }
    }
  }
}
