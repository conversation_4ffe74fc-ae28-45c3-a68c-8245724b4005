import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:farautorentify/models/user_model.dart';
import 'package:farautorentify/utils/constants.dart';

class AuthService {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Get current user
  User? get currentUser => _auth.currentUser;

  // Auth state changes stream
  Stream<User?> get authStateChanges => _auth.authStateChanges();

  // Sign up with email and password (Customer only)
  Future<UserModel?> signUpWithEmailAndPassword({
    required String name,
    required String email,
    required String password,
    required String phone,
    required String cnic,
  }) async {
    try {
      // Create user with Firebase Auth
      UserCredential result = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      User? user = result.user;
      if (user != null) {
        // Create user model
        UserModel userModel = UserModel(
          uid: user.uid,
          name: name,
          email: email,
          phone: phone,
          cnic: cnic,
          role: AppConstants.customerRole,
          createdAt: DateTime.now(),
        );

        // Save user data to Firestore
        await _firestore
            .collection(AppConstants.usersCollection)
            .doc(user.uid)
            .set(userModel.toMap());

        // Save user info locally
        await _saveUserLocally(userModel);

        return userModel;
      }
    } catch (e) {
      print('Error signing up: $e');
      rethrow;
    }
    return null;
  }

  // Sign in with email and password
  Future<UserModel?> signInWithEmailAndPassword({
    required String email,
    required String password,
    required String role,
  }) async {
    try {
      // Check if it's admin login with default credentials
      if (role == AppConstants.adminRole) {
        if (email == AppConstants.defaultAdminEmail &&
            password == AppConstants.defaultAdminPassword) {
          // Create admin user if doesn't exist
          return await _createOrGetAdminUser();
        } else {
          throw FirebaseAuthException(
            code: 'invalid-admin-credentials',
            message: 'Invalid admin credentials',
          );
        }
      }

      // Regular user login
      UserCredential result = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      User? user = result.user;
      if (user != null) {
        // Get user data from Firestore
        DocumentSnapshot userDoc =
            await _firestore
                .collection(AppConstants.usersCollection)
                .doc(user.uid)
                .get();

        if (userDoc.exists) {
          UserModel userModel = UserModel.fromMap(
            userDoc.data() as Map<String, dynamic>,
          );

          // Check if user role matches requested role
          if (userModel.role != role) {
            await _auth.signOut();
            throw FirebaseAuthException(
              code: 'wrong-role',
              message: 'You are not authorized to login as $role',
            );
          }

          // Save user info locally
          await _saveUserLocally(userModel);

          return userModel;
        } else {
          // User document doesn't exist, sign out
          await _auth.signOut();
          throw FirebaseAuthException(
            code: 'user-not-found',
            message: 'User data not found',
          );
        }
      }
    } catch (e) {
      print('Error signing in: $e');
      rethrow;
    }
    return null;
  }

  // Create or get admin user
  Future<UserModel> _createOrGetAdminUser() async {
    try {
      print('Starting admin user creation/retrieval process...');

      // First, try to sign in with existing credentials
      try {
        UserCredential result = await _auth.signInWithEmailAndPassword(
          email: AppConstants.defaultAdminEmail,
          password: AppConstants.defaultAdminPassword,
        );

        User? user = result.user;
        if (user != null) {
          print('Admin user signed in successfully. UID: ${user.uid}');

          // Check if admin document exists in Firestore
          DocumentSnapshot adminDoc =
              await _firestore
                  .collection(AppConstants.usersCollection)
                  .doc(user.uid)
                  .get();

          UserModel adminModel;
          if (adminDoc.exists) {
            print('Admin document found in Firestore');
            adminModel = UserModel.fromMap(
              adminDoc.data() as Map<String, dynamic>,
            );
          } else {
            print('Admin document not found in Firestore, creating new one...');
            // Create admin document if it doesn't exist (this handles your case)
            adminModel = UserModel(
              uid: user.uid,
              name: 'Administrator',
              email: AppConstants.defaultAdminEmail,
              phone: '***********',
              cnic: '*************',
              role: AppConstants.adminRole,
              createdAt: DateTime.now(),
            );

            await _firestore
                .collection(AppConstants.usersCollection)
                .doc(user.uid)
                .set(adminModel.toMap());

            print('Admin document created successfully in Firestore');
          }

          await _saveUserLocally(adminModel);
          print('Admin user data saved locally');
          return adminModel;
        }
      } catch (authError) {
        print('Admin sign-in failed: $authError');

        // If sign-in fails, the admin account might not exist, so create it
        if (authError is FirebaseAuthException &&
            (authError.code == 'user-not-found' ||
                authError.code == 'wrong-password')) {
          print('Creating new admin account...');

          UserCredential result = await _auth.createUserWithEmailAndPassword(
            email: AppConstants.defaultAdminEmail,
            password: AppConstants.defaultAdminPassword,
          );

          User? user = result.user;
          if (user != null) {
            print('New admin account created. UID: ${user.uid}');

            UserModel adminModel = UserModel(
              uid: user.uid,
              name: 'Administrator',
              email: AppConstants.defaultAdminEmail,
              phone: '***********',
              cnic: '*************',
              role: AppConstants.adminRole,
              createdAt: DateTime.now(),
            );

            await _firestore
                .collection(AppConstants.usersCollection)
                .doc(user.uid)
                .set(adminModel.toMap());

            await _saveUserLocally(adminModel);
            print('New admin user setup completed');
            return adminModel;
          }
        } else {
          // Re-throw other authentication errors
          rethrow;
        }
      }
    } catch (e) {
      print('Error in _createOrGetAdminUser: $e');
      rethrow;
    }
    throw Exception('Failed to create or get admin user');
  }

  // Sign out
  Future<void> signOut() async {
    try {
      await _auth.signOut();
      await _clearUserLocally();
    } catch (e) {
      print('Error signing out: $e');
      rethrow;
    }
  }

  // Get current user data
  Future<UserModel?> getCurrentUserData() async {
    try {
      User? user = _auth.currentUser;
      if (user != null) {
        DocumentSnapshot userDoc =
            await _firestore
                .collection(AppConstants.usersCollection)
                .doc(user.uid)
                .get();

        if (userDoc.exists) {
          return UserModel.fromMap(userDoc.data() as Map<String, dynamic>);
        }
      }
    } catch (e) {
      print('Error getting current user data: $e');
    }
    return null;
  }

  // Save user info locally
  Future<void> _saveUserLocally(UserModel user) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setString(AppConstants.userIdKey, user.uid);
    await prefs.setString(AppConstants.userRoleKey, user.role);
    await prefs.setBool(AppConstants.isLoggedInKey, true);
  }

  // Clear user info locally
  Future<void> _clearUserLocally() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.remove(AppConstants.userIdKey);
    await prefs.remove(AppConstants.userRoleKey);
    await prefs.setBool(AppConstants.isLoggedInKey, false);
  }

  // Check if user is logged in locally
  Future<bool> isLoggedIn() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getBool(AppConstants.isLoggedInKey) ?? false;
  }

  // Get user role locally
  Future<String?> getUserRole() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getString(AppConstants.userRoleKey);
  }

  // Reset password
  Future<void> resetPassword(String email) async {
    try {
      await _auth.sendPasswordResetEmail(email: email);
    } catch (e) {
      print('Error resetting password: $e');
      rethrow;
    }
  }

  // Force recreate admin user (for debugging/recovery purposes)
  Future<UserModel> forceRecreateAdmin() async {
    try {
      print('Force recreating admin user...');

      // First, try to delete existing admin if it exists
      try {
        var methods = await _auth.fetchSignInMethodsForEmail(
          AppConstants.defaultAdminEmail,
        );

        if (methods.isNotEmpty) {
          // Sign in to get the user
          UserCredential result = await _auth.signInWithEmailAndPassword(
            email: AppConstants.defaultAdminEmail,
            password: AppConstants.defaultAdminPassword,
          );

          if (result.user != null) {
            // Delete from Firestore
            await _firestore
                .collection(AppConstants.usersCollection)
                .doc(result.user!.uid)
                .delete();

            // Delete from Firebase Auth
            await result.user!.delete();
            print('Existing admin user deleted');
          }
        }
      } catch (e) {
        print('Error deleting existing admin (might not exist): $e');
        // Continue with creation even if deletion fails
      }

      // Create new admin account
      UserCredential result = await _auth.createUserWithEmailAndPassword(
        email: AppConstants.defaultAdminEmail,
        password: AppConstants.defaultAdminPassword,
      );

      User? user = result.user;
      if (user != null) {
        print('New admin account created. UID: ${user.uid}');

        UserModel adminModel = UserModel(
          uid: user.uid,
          name: 'Administrator',
          email: AppConstants.defaultAdminEmail,
          phone: '***********',
          cnic: '*************',
          role: AppConstants.adminRole,
          createdAt: DateTime.now(),
        );

        await _firestore
            .collection(AppConstants.usersCollection)
            .doc(user.uid)
            .set(adminModel.toMap());

        await _saveUserLocally(adminModel);
        print('Admin user force recreation completed');
        return adminModel;
      }
    } catch (e) {
      print('Error in forceRecreateAdmin: $e');
      rethrow;
    }
    throw Exception('Failed to force recreate admin user');
  }
}
