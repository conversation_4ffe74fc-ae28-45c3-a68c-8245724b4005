import 'package:flutter/material.dart';
import 'package:farautorentify/models/booking_model.dart';
import 'package:farautorentify/services/database_service.dart';
import 'package:farautorentify/utils/constants.dart';
import 'package:farautorentify/widgets/custom_button.dart';

class BookingsManagementScreen extends StatefulWidget {
  const BookingsManagementScreen({super.key});

  @override
  State<BookingsManagementScreen> createState() =>
      _BookingsManagementScreenState();
}

class _BookingsManagementScreenState extends State<BookingsManagementScreen> {
  final DatabaseService _databaseService = DatabaseService();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      body: _buildBookingsList(),
    );
  }

  Widget _buildBookingsList() {
    return StreamBuilder<List<BookingModel>>(
      stream: _databaseService.getAllBookings(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(
            child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(
                AppConstants.primaryColor,
              ),
            ),
          );
        }

        if (snapshot.hasError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64,
                  color: AppConstants.errorColor,
                ),
                const SizedBox(height: AppConstants.paddingMedium),
                Text(
                  'Error loading bookings',
                  style: TextStyle(
                    fontSize: AppConstants.fontSizeLarge,
                    color: AppConstants.errorColor,
                  ),
                ),
                const SizedBox(height: AppConstants.paddingSmall),
                Text(
                  snapshot.error.toString(),
                  style: TextStyle(
                    fontSize: AppConstants.fontSizeMedium,
                    color: AppConstants.textSecondaryColor,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

        final bookings = snapshot.data ?? [];

        if (bookings.isEmpty) {
          return _buildEmptyState();
        }

        return ListView.builder(
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          itemCount: bookings.length,
          itemBuilder: (context, index) {
            return _buildBookingCard(bookings[index]);
          },
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.book_online_outlined,
            size: 80,
            color: AppConstants.textSecondaryColor,
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          const Text(
            'No bookings found',
            style: TextStyle(
              fontSize: AppConstants.fontSizeXLarge,
              color: AppConstants.textSecondaryColor,
            ),
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Text(
            'Bookings will appear here when customers make reservations',
            style: TextStyle(
              fontSize: AppConstants.fontSizeMedium,
              color: AppConstants.textSecondaryColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildBookingCard(BookingModel booking) {
    Color statusColor = _getStatusColor(booking.status);

    return Container(
      margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
        boxShadow: [
          BoxShadow(
            color: Colors.black,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingLarge),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Booking Header
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Booking #${booking.id.substring(0, 8)}',
                        style: const TextStyle(
                          fontSize: AppConstants.fontSizeLarge,
                          fontWeight: FontWeight.bold,
                          color: AppConstants.textPrimaryColor,
                        ),
                      ),
                      Text(
                        booking.vehicleName,
                        style: TextStyle(
                          fontSize: AppConstants.fontSizeMedium,
                          color: AppConstants.textSecondaryColor,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppConstants.paddingMedium,
                    vertical: AppConstants.paddingSmall,
                  ),
                  decoration: BoxDecoration(
                    color: statusColor,
                    borderRadius: BorderRadius.circular(
                      AppConstants.borderRadiusLarge,
                    ),
                    border: Border.all(color: statusColor),
                  ),
                  child: Text(
                    booking.status.toUpperCase(),
                    style: TextStyle(
                      color: statusColor,
                      fontSize: AppConstants.fontSizeSmall,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: AppConstants.paddingMedium),

            // Customer Info
            Container(
              padding: const EdgeInsets.all(AppConstants.paddingMedium),
              decoration: BoxDecoration(
                color: AppConstants.backgroundColor,
                borderRadius: BorderRadius.circular(
                  AppConstants.borderRadiusMedium,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Customer Information',
                    style: const TextStyle(
                      fontSize: AppConstants.fontSizeMedium,
                      fontWeight: FontWeight.w600,
                      color: AppConstants.textPrimaryColor,
                    ),
                  ),
                  const SizedBox(height: AppConstants.paddingSmall),
                  _buildInfoRow('Name', booking.customerName),
                  _buildInfoRow('Email', booking.customerEmail),
                  _buildInfoRow('Phone', booking.customerPhone),
                ],
              ),
            ),

            const SizedBox(height: AppConstants.paddingMedium),

            // Booking Details
            Row(
              children: [
                Expanded(
                  child: _buildDetailColumn(
                    'Start Date',
                    '${booking.startDate.day}/${booking.startDate.month}/${booking.startDate.year}',
                  ),
                ),
                Expanded(
                  child: _buildDetailColumn(
                    'End Date',
                    '${booking.endDate.day}/${booking.endDate.month}/${booking.endDate.year}',
                  ),
                ),
                Expanded(
                  child: _buildDetailColumn(
                    'Duration',
                    '${booking.numberOfDays} day${booking.numberOfDays > 1 ? 's' : ''}',
                  ),
                ),
                Expanded(
                  child: _buildDetailColumn(
                    'Total',
                    '\$${booking.totalPrice.toStringAsFixed(0)}',
                  ),
                ),
              ],
            ),

            const SizedBox(height: AppConstants.paddingMedium),

            // Action Buttons
            Row(
              children: [
                if (booking.status == AppConstants.pendingStatus) ...[
                  Expanded(
                    child: SuccessButton(
                      text: 'Confirm',
                      onPressed:
                          () => _updateBookingStatus(
                            booking,
                            AppConstants.confirmedStatus,
                          ),
                    ),
                  ),
                  const SizedBox(width: AppConstants.paddingSmall),
                  Expanded(
                    child: DangerButton(
                      text: 'Cancel',
                      onPressed:
                          () => _updateBookingStatus(
                            booking,
                            AppConstants.cancelledStatus,
                          ),
                    ),
                  ),
                ] else if (booking.status == AppConstants.confirmedStatus) ...[
                  Expanded(
                    child: PrimaryButton(
                      text: 'Mark Complete',
                      onPressed:
                          () => _updateBookingStatus(
                            booking,
                            AppConstants.completedStatus,
                          ),
                    ),
                  ),
                  const SizedBox(width: AppConstants.paddingSmall),
                  Expanded(
                    child: DangerButton(
                      text: 'Cancel',
                      onPressed:
                          () => _updateBookingStatus(
                            booking,
                            AppConstants.cancelledStatus,
                          ),
                    ),
                  ),
                ] else ...[
                  Expanded(
                    child: SecondaryButton(
                      text: 'View Details',
                      onPressed: () => _showBookingDetails(booking),
                    ),
                  ),
                  const SizedBox(width: AppConstants.paddingSmall),
                  Expanded(
                    child: DangerButton(
                      text: 'Delete',
                      onPressed: () => _confirmDeleteBooking(booking),
                    ),
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        children: [
          SizedBox(
            width: 50,
            child: Text(
              '$label:',
              style: TextStyle(
                fontSize: AppConstants.fontSizeSmall,
                color: AppConstants.textSecondaryColor,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: AppConstants.fontSizeSmall,
                color: AppConstants.textPrimaryColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailColumn(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: AppConstants.fontSizeSmall,
            color: AppConstants.textSecondaryColor,
          ),
        ),
        Text(
          value,
          style: const TextStyle(
            fontSize: AppConstants.fontSizeMedium,
            fontWeight: FontWeight.w600,
            color: AppConstants.textPrimaryColor,
          ),
        ),
      ],
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return AppConstants.warningColor;
      case 'confirmed':
        return AppConstants.primaryColor;
      case 'completed':
        return AppConstants.successColor;
      case 'cancelled':
        return AppConstants.errorColor;
      default:
        return AppConstants.textSecondaryColor;
    }
  }

  Future<void> _updateBookingStatus(
    BookingModel booking,
    String newStatus,
  ) async {
    try {
      final updatedBooking = booking.copyWith(
        status: newStatus,
        updatedAt: DateTime.now(),
      );

      await _databaseService.updateBooking(updatedBooking);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Booking ${newStatus.toLowerCase()} successfully'),
            backgroundColor: AppConstants.successColor,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update booking: $e'),
            backgroundColor: AppConstants.errorColor,
          ),
        );
      }
    }
  }

  void _showBookingDetails(BookingModel booking) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text('Booking Details #${booking.id.substring(0, 8)}'),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildDetailRow('Vehicle', booking.vehicleName),
                  _buildDetailRow('Type', booking.vehicleType.toUpperCase()),
                  _buildDetailRow('Customer', booking.customerName),
                  _buildDetailRow('Email', booking.customerEmail),
                  _buildDetailRow('Phone', booking.customerPhone),
                  _buildDetailRow(
                    'Start Date',
                    '${booking.startDate.day}/${booking.startDate.month}/${booking.startDate.year}',
                  ),
                  _buildDetailRow(
                    'End Date',
                    '${booking.endDate.day}/${booking.endDate.month}/${booking.endDate.year}',
                  ),
                  _buildDetailRow('Duration', '${booking.numberOfDays} days'),
                  _buildDetailRow(
                    'Total Price',
                    '\$${booking.totalPrice.toStringAsFixed(2)}',
                  ),
                  _buildDetailRow('Status', booking.status.toUpperCase()),
                  _buildDetailRow('Booking ID', booking.id),
                  _buildDetailRow(
                    'Created',
                    '${booking.createdAt.day}/${booking.createdAt.month}/${booking.createdAt.year}',
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Close'),
              ),
            ],
          ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppConstants.paddingSmall),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  void _confirmDeleteBooking(BookingModel booking) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Delete Booking'),
            content: Text(
              'Are you sure you want to delete booking #${booking.id.substring(0, 8)}? This action cannot be undone.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () async {
                  Navigator.pop(context);
                  await _deleteBooking(booking);
                },
                style: TextButton.styleFrom(
                  foregroundColor: AppConstants.errorColor,
                ),
                child: const Text('Delete'),
              ),
            ],
          ),
    );
  }

  Future<void> _deleteBooking(BookingModel booking) async {
    try {
      await _databaseService.deleteBooking(booking.id);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Booking #${booking.id.substring(0, 8)} has been deleted',
            ),
            backgroundColor: AppConstants.successColor,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to delete booking: $e'),
            backgroundColor: AppConstants.errorColor,
          ),
        );
      }
    }
  }
}
