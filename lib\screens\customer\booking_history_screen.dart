import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:farautorentify/models/booking_model.dart';
import 'package:farautorentify/providers/auth_provider.dart';
import 'package:farautorentify/services/database_service.dart';
import 'package:farautorentify/utils/constants.dart';

class BookingHistoryScreen extends StatefulWidget {
  const BookingHistoryScreen({super.key});

  @override
  State<BookingHistoryScreen> createState() => _BookingHistoryScreenState();
}

class _BookingHistoryScreenState extends State<BookingHistoryScreen> {
  final DatabaseService _databaseService = DatabaseService();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      appBar: AppBar(
        backgroundColor: AppConstants.primaryColor,
        foregroundColor: Colors.white,
        title: const Text(
          'Booking History',
          style: TextStyle(fontWeight: FontWeight.w600),
        ),
        elevation: 0,
      ),
      body: Consumer<AuthProvider>(
        builder: (context, authProvider, child) {
          final userId = authProvider.getCurrentUserId();
          print('DEBUG: BookingHistoryScreen - Current user ID: $userId');
          print(
            'DEBUG: BookingHistoryScreen - Auth provider user: ${authProvider.currentUser?.uid}',
          );

          if (userId == null) {
            return const Center(
              child: Text(
                'Please login to view your booking history',
                style: TextStyle(
                  fontSize: AppConstants.fontSizeLarge,
                  color: AppConstants.textSecondaryColor,
                ),
              ),
            );
          }

          return _buildBookingsList(userId);
        },
      ),
    );
  }

  Widget _buildBookingsList(String userId) {
    return StreamBuilder<List<BookingModel>>(
      stream: _databaseService.getUserBookings(userId),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(
            child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(
                AppConstants.primaryColor,
              ),
            ),
          );
        }

        if (snapshot.hasError) {
          print(
            'DEBUG: Error loading bookings for user $userId: ${snapshot.error}',
          );
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64,
                  color: AppConstants.errorColor,
                ),
                const SizedBox(height: AppConstants.paddingMedium),
                Text(
                  'Error loading bookings',
                  style: TextStyle(
                    fontSize: AppConstants.fontSizeLarge,
                    color: AppConstants.errorColor,
                  ),
                ),
                const SizedBox(height: AppConstants.paddingSmall),
                Text(
                  'Please try again later',
                  style: TextStyle(
                    fontSize: AppConstants.fontSizeMedium,
                    color: AppConstants.textSecondaryColor,
                  ),
                ),
                const SizedBox(height: AppConstants.paddingMedium),
                ElevatedButton(
                  onPressed: () {
                    setState(() {
                      // Trigger rebuild to retry
                    });
                  },
                  child: const Text('Retry'),
                ),
              ],
            ),
          );
        }

        final bookings = snapshot.data ?? [];
        print('DEBUG: Bookings loaded for user $userId: ${bookings.length}');

        // Debug: Print each booking details
        for (var booking in bookings) {
          print(
            'DEBUG: Booking ID: ${booking.id}, Vehicle: ${booking.vehicleName}, Status: ${booking.status}',
          );
        }

        // Sort bookings by creation date (newest first)
        final sortedBookings =
            bookings..sort((a, b) => b.createdAt.compareTo(a.createdAt));

        if (sortedBookings.isEmpty) {
          return _buildEmptyState();
        }

        return ListView.builder(
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          itemCount: sortedBookings.length,
          itemBuilder: (context, index) {
            return _buildBookingCard(sortedBookings[index]);
          },
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.history, size: 80, color: AppConstants.textSecondaryColor),
          const SizedBox(height: AppConstants.paddingMedium),
          const Text(
            'No booking history yet',
            style: TextStyle(
              fontSize: AppConstants.fontSizeXLarge,
              color: AppConstants.textSecondaryColor,
            ),
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Text(
            'Your booking history will appear here after you make your first reservation',
            style: TextStyle(
              fontSize: AppConstants.fontSizeMedium,
              color: AppConstants.textSecondaryColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildBookingCard(BookingModel booking) {
    Color statusColor = _getStatusColor(booking.status);

    return Container(
      margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
        boxShadow: [
          BoxShadow(
            color: Colors.black,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingLarge),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Booking Header
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        booking.vehicleName,
                        style: const TextStyle(
                          fontSize: AppConstants.fontSizeLarge,
                          fontWeight: FontWeight.bold,
                          color: AppConstants.textPrimaryColor,
                        ),
                      ),
                      Text(
                        booking.vehicleType.toUpperCase(),
                        style: TextStyle(
                          fontSize: AppConstants.fontSizeSmall,
                          color: AppConstants.textSecondaryColor,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppConstants.paddingMedium,
                    vertical: AppConstants.paddingSmall,
                  ),
                  decoration: BoxDecoration(
                    color: statusColor,
                    borderRadius: BorderRadius.circular(
                      AppConstants.borderRadiusLarge,
                    ),
                    border: Border.all(color: statusColor),
                  ),
                  child: Text(
                    booking.status.toUpperCase(),
                    style: TextStyle(
                      color: statusColor,
                      fontSize: AppConstants.fontSizeSmall,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: AppConstants.paddingMedium),

            // Booking Details
            Container(
              padding: const EdgeInsets.all(AppConstants.paddingMedium),
              decoration: BoxDecoration(
                color: AppConstants.backgroundColor,
                borderRadius: BorderRadius.circular(
                  AppConstants.borderRadiusMedium,
                ),
              ),
              child: Column(
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: _buildDetailColumn(
                          'Start Date',
                          '${booking.startDate.day}/${booking.startDate.month}/${booking.startDate.year}',
                        ),
                      ),
                      Expanded(
                        child: _buildDetailColumn(
                          'End Date',
                          '${booking.endDate.day}/${booking.endDate.month}/${booking.endDate.year}',
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: AppConstants.paddingMedium),
                  Row(
                    children: [
                      Expanded(
                        child: _buildDetailColumn(
                          'Duration',
                          '${booking.numberOfDays} day${booking.numberOfDays > 1 ? 's' : ''}',
                        ),
                      ),
                      Expanded(
                        child: _buildDetailColumn(
                          'Total Amount',
                          '\$${booking.totalPrice.toStringAsFixed(0)}',
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            const SizedBox(height: AppConstants.paddingMedium),

            // Booking ID and Date
            Row(
              children: [
                Expanded(
                  child: Text(
                    'Booking ID: #${booking.id.substring(0, 8)}',
                    style: TextStyle(
                      fontSize: AppConstants.fontSizeSmall,
                      color: AppConstants.textSecondaryColor,
                    ),
                  ),
                ),
                Text(
                  'Booked: ${booking.createdAt.day}/${booking.createdAt.month}/${booking.createdAt.year}',
                  style: TextStyle(
                    fontSize: AppConstants.fontSizeSmall,
                    color: AppConstants.textSecondaryColor,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailColumn(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: AppConstants.fontSizeSmall,
            color: AppConstants.textSecondaryColor,
          ),
        ),
        Text(
          value,
          style: const TextStyle(
            fontSize: AppConstants.fontSizeMedium,
            fontWeight: FontWeight.w600,
            color: AppConstants.textPrimaryColor,
          ),
        ),
      ],
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return AppConstants.warningColor;
      case 'confirmed':
        return AppConstants.primaryColor;
      case 'completed':
        return AppConstants.successColor;
      case 'cancelled':
        return AppConstants.errorColor;
      default:
        return AppConstants.textSecondaryColor;
    }
  }
}
