// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:farautorentify/models/vehicle_model.dart';
import 'package:farautorentify/services/database_service.dart';
import 'package:farautorentify/utils/constants.dart';
import 'package:farautorentify/utils/validators.dart';
import 'package:farautorentify/widgets/custom_button.dart';
import 'package:farautorentify/widgets/custom_text_field.dart';

class EditVehicleScreen extends StatefulWidget {
  final VehicleModel vehicle;

  const EditVehicleScreen({super.key, required this.vehicle});

  @override
  State<EditVehicleScreen> createState() => _EditVehicleScreenState();
}

class _EditVehicleScreenState extends State<EditVehicleScreen> {
  final _formKey = GlobalKey<FormState>();
  final DatabaseService _databaseService = DatabaseService();

  // Controllers
  late final TextEditingController _nameController;
  late final TextEditingController _brandController;
  late final TextEditingController _modelController;
  late final TextEditingController _yearController;
  late final TextEditingController _priceController;
  late final TextEditingController _imageUrlController;
  late final TextEditingController _descriptionController;

  // Dropdown values
  late String _selectedType;
  late String _selectedCategory;
  late List<String> _selectedFeatures;
  late bool _isAvailable;
  bool _isLoading = false;

  // Categories for each type
  final Map<String, List<String>> _categories = {
    AppConstants.carType: ['Sedan', 'SUV', 'Hatchback', 'Electric'],
    AppConstants.bikeType: ['Sport', 'Cruiser', 'Scooter', 'Electric'],
  };

  @override
  void initState() {
    super.initState();
    _initializeControllers();
  }

  void _initializeControllers() {
    _nameController = TextEditingController(text: widget.vehicle.name);
    _brandController = TextEditingController(text: widget.vehicle.brand);
    _modelController = TextEditingController(text: widget.vehicle.model);
    _yearController = TextEditingController(
      text: widget.vehicle.year.toString(),
    );
    _priceController = TextEditingController(
      text: widget.vehicle.pricePerDay.toString(),
    );
    _imageUrlController = TextEditingController(text: widget.vehicle.imageUrl);
    _descriptionController = TextEditingController(
      text: widget.vehicle.description,
    );

    _selectedType = widget.vehicle.type;
    _selectedCategory =
        widget.vehicle.category.isNotEmpty
            ? widget.vehicle.category
            : _categories[_selectedType]!
                .first; // Use vehicle's category or default
    _selectedFeatures = List.from(widget.vehicle.features);
    _isAvailable = widget.vehicle.isAvailable;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _brandController.dispose();
    _modelController.dispose();
    _yearController.dispose();
    _priceController.dispose();
    _imageUrlController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      appBar: AppBar(
        backgroundColor: AppConstants.primaryColor,
        foregroundColor: Colors.white,
        title: const Text(
          'Edit Vehicle',
          style: TextStyle(fontWeight: FontWeight.w600),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Vehicle Info Header
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(AppConstants.paddingLarge),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(
                    AppConstants.borderRadiusLarge,
                  ),
                ),
                child: Row(
                  children: [
                    Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        color: Colors.grey[300],
                        borderRadius: BorderRadius.circular(
                          AppConstants.borderRadiusMedium,
                        ),
                      ),
                      child: const Icon(
                        Icons.directions_car,
                        size: 30,
                        color: Colors.grey,
                      ),
                    ),
                    const SizedBox(width: AppConstants.paddingMedium),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Editing: ${widget.vehicle.name}',
                            style: const TextStyle(
                              fontSize: AppConstants.fontSizeLarge,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            '${widget.vehicle.brand} ${widget.vehicle.model} (${widget.vehicle.year})',
                            style: TextStyle(
                              fontSize: AppConstants.fontSizeMedium,
                              color: AppConstants.textSecondaryColor,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: AppConstants.paddingLarge),

              // Basic Information Section
              _buildSectionCard(
                title: 'Basic Information',
                children: [
                  NameTextField(
                    controller: _nameController,
                    label: 'Vehicle Name',
                    hint: 'Enter vehicle name',
                    validator: Validators.validateVehicleName,
                  ),
                  const SizedBox(height: AppConstants.paddingMedium),

                  // Type and Category Row
                  Row(
                    children: [
                      Expanded(
                        child: _buildDropdownField(
                          label: 'Type',
                          value: _selectedType,
                          items: [AppConstants.carType, AppConstants.bikeType],
                          onChanged: (value) {
                            setState(() {
                              _selectedType = value!;
                              _selectedCategory =
                                  _categories[_selectedType]!.first;
                              _selectedFeatures
                                  .clear(); // Clear features when type changes
                            });
                          },
                        ),
                      ),
                      const SizedBox(width: AppConstants.paddingMedium),
                      Expanded(
                        child: _buildDropdownField(
                          label: 'Category',
                          value: _selectedCategory,
                          items: _categories[_selectedType]!,
                          onChanged: (value) {
                            setState(() {
                              _selectedCategory = value!;
                            });
                          },
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: AppConstants.paddingMedium),

                  NameTextField(
                    controller: _brandController,
                    label: 'Brand',
                    hint: 'Enter brand name',
                    validator:
                        (value) => Validators.validateRequired(value, 'Brand'),
                  ),
                  const SizedBox(height: AppConstants.paddingMedium),

                  NameTextField(
                    controller: _modelController,
                    label: 'Model',
                    hint: 'Enter model name',
                    validator:
                        (value) => Validators.validateRequired(value, 'Model'),
                  ),
                ],
              ),

              const SizedBox(height: AppConstants.paddingLarge),

              // Details Section
              _buildSectionCard(
                title: 'Details',
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: CustomTextField(
                          label: 'Year',
                          hint: 'Enter year',
                          controller: _yearController,
                          keyboardType: TextInputType.number,
                          validator: Validators.validateYear,
                        ),
                      ),
                      const SizedBox(width: AppConstants.paddingMedium),
                      Expanded(
                        child: CustomTextField(
                          label: 'Price per Day (\$)',
                          hint: 'Enter price',
                          controller: _priceController,
                          keyboardType: TextInputType.number,
                          validator:
                              (value) =>
                                  Validators.validateNumber(value, 'Price'),
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: AppConstants.paddingMedium),

                  CustomTextField(
                    label: 'Image URL',
                    hint: 'Enter image URL',
                    controller: _imageUrlController,
                    validator: Validators.validateUrl,
                  ),

                  const SizedBox(height: AppConstants.paddingMedium),

                  CustomTextField(
                    label: 'Description',
                    hint: 'Enter vehicle description',
                    controller: _descriptionController,
                    maxLines: 3,
                    validator: Validators.validateDescription,
                  ),
                ],
              ),

              const SizedBox(height: AppConstants.paddingLarge),

              // Features Section
              _buildSectionCard(
                title: 'Features',
                children: [_buildFeaturesSelection()],
              ),

              const SizedBox(height: AppConstants.paddingLarge),

              // Availability Section
              _buildSectionCard(
                title: 'Availability',
                children: [
                  SwitchListTile(
                    title: const Text('Available for Booking'),
                    subtitle: Text(
                      _isAvailable
                          ? 'Vehicle is available for customers to book'
                          : 'Vehicle is not available for booking',
                    ),
                    value: _isAvailable,
                    onChanged: (value) {
                      setState(() {
                        _isAvailable = value;
                      });
                    },
                    activeColor: AppConstants.primaryColor,
                  ),
                ],
              ),

              const SizedBox(height: AppConstants.paddingXLarge),

              // Update Button
              PrimaryButton(
                text: 'Update Vehicle',
                width: double.infinity,
                isLoading: _isLoading,
                onPressed: _handleUpdateVehicle,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionCard({
    required String title,
    required List<Widget> children,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: AppConstants.fontSizeXLarge,
              fontWeight: FontWeight.bold,
              color: AppConstants.textPrimaryColor,
            ),
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          ...children,
        ],
      ),
    );
  }

  Widget _buildDropdownField({
    required String label,
    required String value,
    required List<String> items,
    required ValueChanged<String?> onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: AppConstants.fontSizeMedium,
            fontWeight: FontWeight.w500,
            color: AppConstants.textPrimaryColor,
          ),
        ),
        const SizedBox(height: AppConstants.paddingSmall),
        DropdownButtonFormField<String>(
          value: value,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(
                AppConstants.borderRadiusMedium,
              ),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: AppConstants.paddingMedium,
              vertical: AppConstants.paddingMedium,
            ),
          ),
          items:
              items.map((item) {
                return DropdownMenuItem(
                  value: item,
                  child: Text(item.toUpperCase()),
                );
              }).toList(),
          onChanged: onChanged,
        ),
      ],
    );
  }

  Widget _buildFeaturesSelection() {
    final availableFeatures =
        _selectedType == AppConstants.carType
            ? AppConstants.carFeatures
            : AppConstants.bikeFeatures;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Select Features:',
          style: TextStyle(
            fontSize: AppConstants.fontSizeMedium,
            fontWeight: FontWeight.w500,
            color: AppConstants.textPrimaryColor,
          ),
        ),
        const SizedBox(height: AppConstants.paddingSmall),
        Wrap(
          spacing: AppConstants.paddingSmall,
          runSpacing: AppConstants.paddingSmall,
          children:
              availableFeatures.map((feature) {
                final isSelected = _selectedFeatures.contains(feature);
                return FilterChip(
                  label: Text(feature),
                  selected: isSelected,
                  onSelected: (selected) {
                    setState(() {
                      if (selected) {
                        _selectedFeatures.add(feature);
                      } else {
                        _selectedFeatures.remove(feature);
                      }
                    });
                  },
                  selectedColor: AppConstants.primaryColor.withOpacity(0.2),
                  checkmarkColor: AppConstants.primaryColor,
                );
              }).toList(),
        ),
        if (_selectedFeatures.isEmpty) ...[
          const SizedBox(height: AppConstants.paddingSmall),
          Text(
            'Please select at least one feature',
            style: TextStyle(
              fontSize: AppConstants.fontSizeSmall,
              color: AppConstants.textSecondaryColor,
            ),
          ),
        ],
      ],
    );
  }

  Future<void> _handleUpdateVehicle() async {
    if (!_formKey.currentState!.validate()) return;

    if (_selectedFeatures.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select at least one feature'),
          backgroundColor: AppConstants.warningColor,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final updatedVehicle = widget.vehicle.copyWith(
        name: _nameController.text.trim(),
        type: _selectedType,
        category: _selectedCategory,
        brand: _brandController.text.trim(),
        model: _modelController.text.trim(),
        year: int.parse(_yearController.text),
        pricePerDay: double.parse(_priceController.text),
        imageUrl: _imageUrlController.text.trim(),
        description: _descriptionController.text.trim(),
        isAvailable: _isAvailable,
        features: _selectedFeatures,
        updatedAt: DateTime.now(),
      );

      await _databaseService.updateVehicle(updatedVehicle);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Vehicle updated successfully!'),
            backgroundColor: AppConstants.successColor,
          ),
        );
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update vehicle: $e'),
            backgroundColor: AppConstants.errorColor,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
