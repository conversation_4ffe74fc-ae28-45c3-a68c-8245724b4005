import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:farautorentify/providers/auth_provider.dart';
import 'package:farautorentify/screens/auth/signup_screen.dart';
import 'package:farautorentify/screens/customer/customer_home_screen.dart';
import 'package:farautorentify/screens/admin/admin_home_screen.dart';
import 'package:farautorentify/utils/constants.dart';
import 'package:farautorentify/utils/validators.dart';
import 'package:farautorentify/widgets/custom_button.dart';
import 'package:farautorentify/widgets/custom_text_field.dart';

class LoginScreen extends StatefulWidget {
  final String role;

  const LoginScreen({super.key, required this.role});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(
            Icons.arrow_back,
            color: AppConstants.textPrimaryColor,
          ),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppConstants.paddingLarge),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: AppConstants.paddingLarge),

                // Header
                _buildHeader(),

                const SizedBox(height: AppConstants.paddingXLarge),

                // Login Form
                _buildLoginForm(),

                const SizedBox(height: AppConstants.paddingLarge),

                // Login Button
                _buildLoginButton(),

                const SizedBox(height: AppConstants.paddingMedium),

                // Forgot Password
                _buildForgotPassword(),

                if (widget.role == AppConstants.customerRole) ...[
                  const SizedBox(height: AppConstants.paddingXLarge),
                  _buildSignUpSection(),
                ],

                // Admin Info
                if (widget.role == AppConstants.adminRole) ...[
                  const SizedBox(height: AppConstants.paddingXLarge),
                  _buildAdminInfo(),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '${widget.role == AppConstants.adminRole ? 'Admin' : 'Customer'} Login',
          style: const TextStyle(
            fontSize: AppConstants.fontSizeHeading,
            fontWeight: FontWeight.bold,
            color: AppConstants.textPrimaryColor,
          ),
        ),
        const SizedBox(height: AppConstants.paddingSmall),
        Text(
          'Welcome back! Please sign in to continue.',
          style: TextStyle(
            fontSize: AppConstants.fontSizeLarge,
            color: AppConstants.textSecondaryColor,
          ),
        ),
      ],
    );
  }

  Widget _buildLoginForm() {
    return Column(
      children: [
        EmailTextField(
          controller: _emailController,
          validator: Validators.validateEmail,
        ),
        const SizedBox(height: AppConstants.paddingMedium),
        PasswordTextField(
          controller: _passwordController,
          validator: Validators.validatePassword,
        ),
      ],
    );
  }

  Widget _buildLoginButton() {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        return PrimaryButton(
          text: 'Sign In',
          width: double.infinity,
          isLoading: authProvider.isLoading,
          onPressed: () => _handleLogin(authProvider),
        );
      },
    );
  }

  Widget _buildForgotPassword() {
    return Center(
      child: TextButton(
        onPressed: _handleForgotPassword,
        child: Text(
          'Forgot Password?',
          style: TextStyle(
            color: AppConstants.primaryColor,
            fontSize: AppConstants.fontSizeMedium,
          ),
        ),
      ),
    );
  }

  Widget _buildSignUpSection() {
    return Column(
      children: [
        const Divider(),
        const SizedBox(height: AppConstants.paddingMedium),
        Text(
          "Don't have an account?",
          style: TextStyle(
            fontSize: AppConstants.fontSizeMedium,
            color: AppConstants.textSecondaryColor,
          ),
        ),
        const SizedBox(height: AppConstants.paddingMedium),
        SecondaryButton(
          text: 'Create Account',
          width: double.infinity,
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const SignUpScreen()),
            );
          },
        ),
      ],
    );
  }

  Widget _buildAdminInfo() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
        border: Border.all(color: AppConstants.primaryColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.info_outline,
                color: AppConstants.primaryColor,
                size: 20,
              ),
              const SizedBox(width: AppConstants.paddingSmall),
              Text(
                'Admin Access',
                style: TextStyle(
                  fontSize: AppConstants.fontSizeMedium,
                  fontWeight: FontWeight.w600,
                  color: AppConstants.primaryColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Text(
            'Use the default admin credentials to access the admin panel:',
            style: TextStyle(
              fontSize: AppConstants.fontSizeSmall,
              color: AppConstants.textSecondaryColor,
            ),
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Text(
            'Email: ${AppConstants.defaultAdminEmail}',
            style: TextStyle(
              fontSize: AppConstants.fontSizeSmall,
              fontWeight: FontWeight.w500,
            ),
          ),
          Text(
            'Password: ${AppConstants.defaultAdminPassword}',
            style: TextStyle(
              fontSize: AppConstants.fontSizeSmall,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _handleLogin(AuthProvider authProvider) async {
    if (!_formKey.currentState!.validate()) return;

    final success = await authProvider.signIn(
      email: _emailController.text.trim(),
      password: _passwordController.text,
      role: widget.role,
    );

    if (success && mounted) {
      // Navigate to appropriate home screen
      if (widget.role == AppConstants.adminRole) {
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(builder: (context) => const AdminHomeScreen()),
        );
      } else {
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(builder: (context) => const CustomerHomeScreen()),
        );
      }
    } else if (authProvider.errorMessage != null && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(authProvider.errorMessage!),
          backgroundColor: AppConstants.errorColor,
        ),
      );
    }
  }

  Future<void> _handleForgotPassword() async {
    final email = _emailController.text.trim();
    if (email.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter your email address'),
          backgroundColor: AppConstants.warningColor,
        ),
      );
      return;
    }

    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final success = await authProvider.resetPassword(email);

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            success
                ? 'Password reset email sent to $email'
                : authProvider.errorMessage ?? 'Failed to send reset email',
          ),
          backgroundColor:
              success ? AppConstants.successColor : AppConstants.errorColor,
        ),
      );
    }
  }
}
