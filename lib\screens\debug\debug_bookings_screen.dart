import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:provider/provider.dart';
import 'package:farautorentify/providers/auth_provider.dart';
import 'package:farautorentify/utils/constants.dart';

class DebugBookingsScreen extends StatelessWidget {
  const DebugBookingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Debug: All Bookings'),
        backgroundColor: AppConstants.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: Consumer<AuthProvider>(
        builder: (context, authProvider, child) {
          final currentUserId = authProvider.getCurrentUserId();
          
          return Column(
            children: [
              // User Info
              Container(
                padding: const EdgeInsets.all(16),
                color: Colors.blue[50],
                child: Column(
                  children: [
                    Row(
                      children: [
                        const Icon(Icons.person, color: Colors.blue),
                        const SizedBox(width: 8),
                        Text(
                          'Current User ID: ${currentUserId ?? "NOT LOGGED IN"}',
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        const Icon(Icons.email, color: Colors.blue),
                        const SizedBox(width: 8),
                        Text(
                          'Email: ${authProvider.currentUser?.email ?? "N/A"}',
                          style: const TextStyle(fontSize: 12),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              
              // All Bookings
              Expanded(
                child: StreamBuilder<QuerySnapshot>(
                  stream: FirebaseFirestore.instance
                      .collection(AppConstants.bookingsCollection)
                      .snapshots(),
                  builder: (context, snapshot) {
                    if (snapshot.connectionState == ConnectionState.waiting) {
                      return const Center(child: CircularProgressIndicator());
                    }

                    if (snapshot.hasError) {
                      return Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Icon(Icons.error, size: 64, color: Colors.red),
                            const SizedBox(height: 16),
                            Text('Error: ${snapshot.error}'),
                          ],
                        ),
                      );
                    }

                    final allDocs = snapshot.data?.docs ?? [];
                    final userBookings = allDocs.where((doc) {
                      final data = doc.data() as Map<String, dynamic>;
                      return data['userId'] == currentUserId;
                    }).toList();

                    return Column(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(16),
                          color: Colors.green[50],
                          child: Column(
                            children: [
                              Row(
                                children: [
                                  const Icon(Icons.info, color: Colors.green),
                                  const SizedBox(width: 8),
                                  Text(
                                    'Total Bookings in DB: ${allDocs.length}',
                                    style: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 8),
                              Row(
                                children: [
                                  const Icon(Icons.person_outline, color: Colors.green),
                                  const SizedBox(width: 8),
                                  Text(
                                    'Your Bookings: ${userBookings.length}',
                                    style: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.green,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        
                        Expanded(
                          child: allDocs.isEmpty
                              ? const Center(
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(Icons.warning, size: 64, color: Colors.orange),
                                      SizedBox(height: 16),
                                      Text(
                                        'No bookings found in database!',
                                        style: TextStyle(fontSize: 18),
                                      ),
                                      SizedBox(height: 8),
                                      Text(
                                        'Create some bookings first.',
                                        style: TextStyle(color: Colors.grey),
                                      ),
                                    ],
                                  ),
                                )
                              : ListView.builder(
                                  padding: const EdgeInsets.all(16),
                                  itemCount: allDocs.length,
                                  itemBuilder: (context, index) {
                                    final doc = allDocs[index];
                                    final data = doc.data() as Map<String, dynamic>;
                                    final isUserBooking = data['userId'] == currentUserId;
                                    
                                    return Card(
                                      margin: const EdgeInsets.only(bottom: 12),
                                      color: isUserBooking ? Colors.green[50] : Colors.grey[50],
                                      child: Padding(
                                        padding: const EdgeInsets.all(16),
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Row(
                                              children: [
                                                Expanded(
                                                  child: Text(
                                                    data['vehicleName'] ?? 'Unknown Vehicle',
                                                    style: const TextStyle(
                                                      fontSize: 18,
                                                      fontWeight: FontWeight.bold,
                                                    ),
                                                  ),
                                                ),
                                                Container(
                                                  padding: const EdgeInsets.symmetric(
                                                    horizontal: 8,
                                                    vertical: 4,
                                                  ),
                                                  decoration: BoxDecoration(
                                                    color: isUserBooking
                                                        ? Colors.green[100]
                                                        : Colors.grey[200],
                                                    borderRadius: BorderRadius.circular(12),
                                                  ),
                                                  child: Text(
                                                    isUserBooking ? 'YOUR BOOKING' : 'OTHER USER',
                                                    style: TextStyle(
                                                      fontSize: 10,
                                                      fontWeight: FontWeight.bold,
                                                      color: isUserBooking
                                                          ? Colors.green[800]
                                                          : Colors.grey[600],
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                            const SizedBox(height: 8),
                                            Text('Document ID: ${doc.id}'),
                                            Text('User ID: ${data['userId'] ?? 'NOT SET'}'),
                                            Text('Status: ${data['status'] ?? 'NOT SET'}'),
                                            Text('Vehicle Type: ${data['vehicleType'] ?? 'NOT SET'}'),
                                            Text('Total Price: \$${data['totalPrice'] ?? 'NOT SET'}'),
                                            if (data['createdAt'] != null)
                                              Text('Created: ${DateTime.fromMillisecondsSinceEpoch(data['createdAt']).toString()}'),
                                          ],
                                        ),
                                      ),
                                    );
                                  },
                                ),
                        ),
                      ],
                    );
                  },
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}
