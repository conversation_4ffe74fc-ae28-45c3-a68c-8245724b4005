import 'package:flutter/material.dart';

class AppConstants {
  // App Info
  static const String appName = 'FarAutoRentify';
  static const String appVersion = '1.0.0';

  // User Roles
  static const String adminRole = 'admin';
  static const String customerRole = 'customer';

  // Vehicle Types
  static const String carType = 'car';
  static const String bikeType = 'bike';

  // Booking Status
  static const String pendingStatus = 'pending';
  static const String confirmedStatus = 'confirmed';
  static const String completedStatus = 'completed';
  static const String cancelledStatus = 'cancelled';

  // Colors
  static const Color primaryColor = Color(0xFF2196F3);
  static const Color secondaryColor = Color(0xFF03DAC6);
  static const Color errorColor = Color(0xFFB00020);
  static const Color successColor = Color(0xFF4CAF50);
  static const Color warningColor = Color(0xFFFF9800);
  static const Color backgroundColor = Color(0xFFF5F5F5);
  static const Color cardColor = Color(0xFFFFFFFF);
  static const Color textPrimaryColor = Color(0xFF212121);
  static const Color textSecondaryColor = Color(0xFF757575);

  // Spacing
  static const double paddingSmall = 8.0;
  static const double paddingMedium = 16.0;
  static const double paddingLarge = 24.0;
  static const double paddingXLarge = 32.0;

  // Border Radius
  static const double borderRadiusSmall = 4.0;
  static const double borderRadiusMedium = 8.0;
  static const double borderRadiusLarge = 12.0;
  static const double borderRadiusXLarge = 16.0;

  // Font Sizes
  static const double fontSizeSmall = 12.0;
  static const double fontSizeMedium = 14.0;
  static const double fontSizeLarge = 16.0;
  static const double fontSizeXLarge = 18.0;
  static const double fontSizeXXLarge = 20.0;
  static const double fontSizeTitle = 24.0;
  static const double fontSizeHeading = 28.0;

  // Animation Durations
  static const Duration animationDurationShort = Duration(milliseconds: 200);
  static const Duration animationDurationMedium = Duration(milliseconds: 300);
  static const Duration animationDurationLong = Duration(milliseconds: 500);

  // Firestore Collections
  static const String usersCollection = 'users';
  static const String vehiclesCollection = 'vehicles';
  static const String bookingsCollection = 'bookings';
  static const String wishlistCollection = 'wishlist';

  // SharedPreferences Keys
  static const String userRoleKey = 'user_role';
  static const String userIdKey = 'user_id';
  static const String isLoggedInKey = 'is_logged_in';

  // Validation
  static const int minPasswordLength = 6;
  static const int maxPasswordLength = 20;
  static const int cnicLength = 13;
  static const int phoneLength = 11;

  // Default Admin Credentials
  static const String defaultAdminEmail = '<EMAIL>';
  static const String defaultAdminPassword = '123456';

  // Error Messages
  static const String networkError =
      'Network error. Please check your connection.';
  static const String unknownError =
      'An unknown error occurred. Please try again.';
  static const String authError =
      'Authentication failed. Please check your credentials.';
  static const String permissionError =
      'You do not have permission to perform this action.';

  // Success Messages
  static const String loginSuccess = 'Login successful!';
  static const String signupSuccess = 'Account created successfully!';
  static const String bookingSuccess = 'Booking created successfully!';
  static const String updateSuccess = 'Updated successfully!';
  static const String deleteSuccess = 'Deleted successfully!';

  // Vehicle Features
  static const List<String> carFeatures = [
    'Air Conditioning',
    'GPS Navigation',
    'Bluetooth',
    'USB Charging',
    'Backup Camera',
    'Automatic Transmission',
    'Manual Transmission',
    'Sunroof',
    'Leather Seats',
    'Cruise Control',
  ];

  static const List<String> bikeFeatures = [
    'Electric Start',
    'Disc Brakes',
    'LED Headlights',
    'Digital Speedometer',
    'USB Charging Port',
    'Anti-lock Braking System (ABS)',
    'Fuel Injection',
    'Alloy Wheels',
    'Storage Compartment',
    'Windshield',
  ];

  // Dummy Image URLs (replace with actual images)
  static const String defaultCarImage =
      'https://images.unsplash.com/photo-*************-ae79c964c9d7?fm=jpg&q=60&w=3000&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8Y2Fyc3xlbnwwfHwwfHx8MA%3D%3D';
  static const String defaultBikeImage =
      'https://via.placeholder.com/300x200?text=Bike';
  static const String defaultUserAvatar =
      'https://via.placeholder.com/100x100?text=User';
}
