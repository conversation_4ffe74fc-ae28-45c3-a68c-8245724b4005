import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class AdminProfileScreen extends StatefulWidget {
  const AdminProfileScreen({super.key});

  @override
  State<AdminProfileScreen> createState() => _AdminProfileScreenState();
}

class _AdminProfileScreenState extends State<AdminProfileScreen> {
  String? name;
  String? email;
  String? contact;
  bool isLoading = true;
  String error = '';

  @override
  void initState() {
    super.initState();
    loadAdminData();
  }

  Future<void> loadAdminData() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        setState(() {
          error = 'No admin is logged in.';
          isLoading = false;
        });
        return;
      }

      email = user.email;

      final doc =
          await FirebaseFirestore.instance
              .collection('users') // ← UPDATED collection
              .doc(user.uid)
              .get();

      if (doc.exists) {
        final data = doc.data();
        setState(() {
          name = data?['name'] ?? 'No Name';
          contact =
              data?['phone'] ??
              'No Contact'; // or 'contact' depending on your field name
          isLoading = false;
        });
      } else {
        setState(() {
          error = 'Admin data not found in users collection.';
          isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        error = 'Error fetching admin data: $e';
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Admin Profile')),
      body:
          isLoading
              ? const Center(child: CircularProgressIndicator())
              : error.isNotEmpty
              ? Center(child: Text(error))
              : Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Name: $name', style: const TextStyle(fontSize: 18)),
                    const SizedBox(height: 10),
                    Text('Email: $email', style: const TextStyle(fontSize: 18)),
                    const SizedBox(height: 10),
                    Text(
                      'Contact: $contact',
                      style: const TextStyle(fontSize: 18),
                    ),
                  ],
                ),
              ),
    );
  }
}
